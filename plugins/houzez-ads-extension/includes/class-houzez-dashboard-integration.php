<?php
/**
 * Houzez Dashboard Integration
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

/**
 * Houzez Dashboard Integration class.
 *
 * Integrates ad campaign functionality into the Houzez user dashboard.
 */
class Houzez_Dashboard_Integration {

	/**
	 * Initialize the dashboard integration.
	 */
	public function __construct() {
		add_action( 'init', array( $this, 'init_dashboard_integration' ) );
	}

	/**
	 * Initialize dashboard integration hooks.
	 */
	public function init_dashboard_integration() {
		if ( ! houzez_ads_is_houzez_theme_active() ) {
			return;
		}

		// Add dashboard menu items
		add_filter( 'houzez_dashboard_nav_menu', array( $this, 'add_dashboard_menu_items' ) );
		
		// Handle dashboard page content
		add_action( 'houzez_dashboard_content', array( $this, 'handle_dashboard_content' ) );
		
		// Add dashboard scripts and styles
		add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_dashboard_assets' ) );
		
		// Handle form submissions
		add_action( 'wp_ajax_houzez_ads_create_campaign', array( $this, 'handle_campaign_creation' ) );
		add_action( 'wp_ajax_houzez_ads_delete_campaign', array( $this, 'handle_campaign_deletion' ) );

		// Template integration
		add_filter( 'page_template', array( $this, 'load_ads_dashboard_template' ) );
		add_filter( 'theme_page_templates', array( $this, 'register_ads_dashboard_template' ) );
		add_filter( 'page_attributes_dropdown_pages_args', array( $this, 'register_ads_dashboard_template_cache' ) );
		add_filter( 'wp_insert_post_data', array( $this, 'register_ads_dashboard_template_cache' ) );
		add_filter( 'houzez_is_dashboard_filter', array( $this, 'add_ads_dashboard_to_houzez_filter' ) );
		add_action( 'wp_footer', array( $this, 'inject_ads_menu_into_dashboard' ) );
	}

	/**
	 * Add dashboard menu items.
	 *
	 * @param array $menu_items Existing menu items.
	 * @return array Modified menu items.
	 */
	public function add_dashboard_menu_items( $menu_items ) {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return $menu_items;
		}

		// Get campaign counts for menu badges
		$current_user_id = get_current_user_id();
		$campaign_counts = houzez_ads_get_user_campaign_counts( $current_user_id );

		$ads_menu_items = array(
			'ad_campaigns' => array(
				'title' => __( 'Ad Campaigns', 'houzez-ads-extension' ),
				'icon' => 'houzez-icon icon-megaphone',
				'url' => add_query_arg( 'dashboard_page', 'campaigns', houzez_ads_get_dashboard_url() ),
				'active' => isset( $_GET['dashboard_page'] ) && in_array( $_GET['dashboard_page'], array( 'campaigns', 'ad_campaigns' ) ),
				'badge' => $campaign_counts['total']
			),
			'create_campaign' => array(
				'title' => __( 'Create Campaign', 'houzez-ads-extension' ),
				'icon' => 'houzez-icon icon-add-circle',
				'url' => add_query_arg( 'dashboard_page', 'create_campaign', houzez_ads_get_dashboard_url() ),
				'active' => isset( $_GET['dashboard_page'] ) && $_GET['dashboard_page'] === 'create_campaign'
			),
			'campaign_insights' => array(
				'title' => __( 'Campaign Insights', 'houzez-ads-extension' ),
				'icon' => 'houzez-icon icon-analytics-pie-1',
				'url' => add_query_arg( 'dashboard_page', 'insights', houzez_ads_get_dashboard_url() ),
				'active' => isset( $_GET['dashboard_page'] ) && $_GET['dashboard_page'] === 'insights'
			),
			'ad_credits' => array(
				'title' => __( 'Credits & Billing', 'houzez-ads-extension' ),
				'icon' => 'houzez-icon icon-accounting-document',
				'url' => add_query_arg( 'dashboard_page', 'credits', houzez_ads_get_dashboard_url() ),
				'active' => isset( $_GET['dashboard_page'] ) && $_GET['dashboard_page'] === 'credits',
				'badge' => houzez_ads_get_user_credits( $current_user_id )
			)
		);

		// Insert after properties menu item
		$position = array_search( 'properties', array_keys( $menu_items ) );
		if ( $position !== false ) {
			$menu_items = array_slice( $menu_items, 0, $position + 1, true ) +
						  $ads_menu_items +
						  array_slice( $menu_items, $position + 1, null, true );
		} else {
			$menu_items = array_merge( $menu_items, $ads_menu_items );
		}

		return $menu_items;
	}

	/**
	 * Handle dashboard content based on page.
	 */
	public function handle_dashboard_content() {
		if ( ! isset( $_GET['dashboard_page'] ) ) {
			return;
		}

		$page = sanitize_text_field( $_GET['dashboard_page'] );

		switch ( $page ) {
			case 'campaigns':
			case 'ad_campaigns':
				$this->display_campaigns_page();
				break;
			case 'create_campaign':
				$this->display_create_campaign_page();
				break;
			case 'insights':
				$this->display_insights_page();
				break;
			case 'credits':
				$this->display_credits_page();
				break;
		}
	}

	/**
	 * Load ads dashboard template for pages using the ads dashboard template.
	 *
	 * @param string $template Current template.
	 * @return string Modified template path.
	 */
	public function load_ads_dashboard_template( $template ) {
		global $post;

		if ( is_page() && $post ) {
			$page_template = get_page_template_slug( $post->ID );

			if ( $page_template === 'user_dashboard_ads.php' ) {
				$ads_template = HOUZEZ_ADS_EXTENSION_PATH . 'templates/user_dashboard_ads.php';

				if ( file_exists( $ads_template ) ) {
					return $ads_template;
				}
			}
		}

		return $template;
	}

	/**
	 * Register the ads dashboard template in the page template dropdown.
	 *
	 * @param array $page_templates Array of page templates.
	 * @return array Modified array of page templates.
	 */
	public function register_ads_dashboard_template( $page_templates ) {
		$page_templates['user_dashboard_ads.php'] = __( 'Ads Dashboard', 'houzez-ads-extension' );
		return $page_templates;
	}

	/**
	 * Register ads dashboard template in cache to ensure it appears in dropdown.
	 *
	 * @param array $atts Page attributes.
	 * @return array Page attributes.
	 */
	public function register_ads_dashboard_template_cache( $atts ) {
		// Create the key used for the themes cache
		$cache_key = 'page_templates-' . md5( get_theme_root() . '/' . get_stylesheet() );

		// Retrieve the cache list
		$templates = wp_get_theme()->get_page_templates();

		if ( empty( $templates ) ) {
			$templates = array();
		}

		// Clear the old cache
		wp_cache_delete( $cache_key, 'themes' );

		// Add our template to the list
		$templates['user_dashboard_ads.php'] = __( 'Ads Dashboard', 'houzez-ads-extension' );

		// Add the modified cache to allow WordPress to pick it up
		wp_cache_add( $cache_key, $templates, 'themes', 1800 );

		return $atts;
	}

	/**
	 * Add ads dashboard template to Houzez dashboard filter.
	 *
	 * @param array $templates Array of dashboard templates.
	 * @return array Modified array of dashboard templates.
	 */
	public function add_ads_dashboard_to_houzez_filter( $templates ) {
		$templates[] = 'user_dashboard_ads.php';
		return $templates;
	}

	/**
	 * Inject ads menu items into the Houzez dashboard menu.
	 */
	public function inject_ads_menu_into_dashboard() {
		// Only inject on dashboard pages
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return;
		}

		// Check if this is a dashboard page
		if ( ! function_exists( 'houzez_is_dashboard' ) || ! houzez_is_dashboard() ) {
			return;
		}

		$current_page_url = get_permalink();
		$ads_dashboard_url = '';

		// Find the ads dashboard page
		$ads_pages = get_posts( array(
			'post_type' => 'page',
			'meta_query' => array(
				array(
					'key' => '_wp_page_template',
					'value' => 'user_dashboard_ads.php'
				)
			),
			'posts_per_page' => 1
		) );

		if ( ! empty( $ads_pages ) ) {
			$ads_dashboard_url = get_permalink( $ads_pages[0]->ID );
		}

		if ( empty( $ads_dashboard_url ) ) {
			return;
		}

		// Inject ads menu items via JavaScript
		?>
		<script>
		jQuery(document).ready(function($) {
			// Add ads menu section to dashboard sidebar
			var adsMenuHtml = '<div class="nav-box">' +
				'<h5><?php _e( "Advertising", "houzez-ads-extension" ); ?></h5>' +
				'<ul>' +
					'<li>' +
						'<a href="<?php echo esc_js( $ads_dashboard_url ); ?>">' +
							'<i class="houzez-icon icon-megaphone"></i>' +
							'<span><?php _e( "Ad Campaigns", "houzez-ads-extension" ); ?></span>' +
						'</a>' +
					'</li>' +
					'<li>' +
						'<a href="<?php echo esc_js( add_query_arg( "ads_page", "create_campaign", $ads_dashboard_url ) ); ?>">' +
							'<i class="houzez-icon icon-add-circle"></i>' +
							'<span><?php _e( "Create Campaign", "houzez-ads-extension" ); ?></span>' +
						'</a>' +
					'</li>' +
					'<li>' +
						'<a href="<?php echo esc_js( add_query_arg( "ads_page", "insights", $ads_dashboard_url ) ); ?>">' +
							'<i class="houzez-icon icon-analytics-pie-1"></i>' +
							'<span><?php _e( "Campaign Insights", "houzez-ads-extension" ); ?></span>' +
						'</a>' +
					'</li>' +
					'<li>' +
						'<a href="<?php echo esc_js( add_query_arg( "ads_page", "credits", $ads_dashboard_url ) ); ?>">' +
							'<i class="houzez-icon icon-accounting-document"></i>' +
							'<span><?php _e( "Buy Credits", "houzez-ads-extension" ); ?></span>' +
						'</a>' +
					'</li>' +
				'</ul>' +
			'</div>';

			// Insert after the last nav-box in the dashboard sidebar
			$('.dashboard-sidebar .nav-box').last().after(adsMenuHtml);
		});
		</script>
		<?php
	}

	/**
	 * Display campaigns page.
	 */
	private function display_campaigns_page() {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			echo '<div class="alert alert-warning">' . __( 'You do not have permission to view campaigns.', 'houzez-ads-extension' ) . '</div>';
			return;
		}

		echo do_shortcode( '[houzez_ads_dashboard]' );
	}

	/**
	 * Display create campaign page.
	 */
	private function display_create_campaign_page() {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			echo '<div class="alert alert-warning">' . __( 'You do not have permission to create campaigns.', 'houzez-ads-extension' ) . '</div>';
			return;
		}

		echo do_shortcode( '[houzez_ads_upload]' );
	}

	/**
	 * Display insights page.
	 */
	private function display_insights_page() {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			echo '<div class="alert alert-warning">' . __( 'You do not have permission to view insights.', 'houzez-ads-extension' ) . '</div>';
			return;
		}

		echo do_shortcode( '[houzez_ads_insights]' );
	}

	/**
	 * Display credits page.
	 */
	private function display_credits_page() {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			echo '<div class="alert alert-warning">' . __( 'You do not have permission to view credits.', 'houzez-ads-extension' ) . '</div>';
			return;
		}

		echo do_shortcode( '[houzez_ads_credits]' );
	}

	/**
	 * Enqueue dashboard assets.
	 */
	public function enqueue_dashboard_assets() {
		// Only load on ads dashboard template or other dashboard pages
		if ( ! is_page_template( 'user_dashboard_ads.php' ) &&
			 ! is_page_template( 'template/user_dashboard.php' ) &&
			 ! is_page_template( 'template/user_dashboard_profile.php' ) ) {
			return;
		}

		wp_enqueue_style( 
			'houzez-ads-dashboard', 
			HOUZEZ_ADS_EXTENSION_URL . 'frontend/css/houzez-ads-dashboard.css', 
			array(), 
			HOUZEZ_ADS_EXTENSION_VERSION 
		);

		// Enqueue Chart.js for analytics
		wp_enqueue_script(
			'chart-js',
			'https://cdn.jsdelivr.net/npm/chart.js',
			array(),
			'3.9.1',
			true
		);

		wp_enqueue_script(
			'houzez-ads-dashboard',
			HOUZEZ_ADS_EXTENSION_URL . 'frontend/js/houzez-ads-dashboard.js',
			array( 'jquery', 'chart-js' ),
			HOUZEZ_ADS_EXTENSION_VERSION,
			true
		);

		wp_localize_script( 'houzez-ads-dashboard', 'houzez_ads_dashboard', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'houzez_ads_dashboard_nonce' ),
			'strings' => array(
				'confirm_delete' => __( 'Are you sure you want to delete this campaign?', 'houzez-ads-extension' ),
				'deleting' => __( 'Deleting...', 'houzez-ads-extension' ),
				'deleted' => __( 'Campaign deleted successfully!', 'houzez-ads-extension' ),
				'error' => __( 'An error occurred. Please try again.', 'houzez-ads-extension' )
			)
		));
	}

	/**
	 * Handle campaign creation AJAX.
	 */
	public function handle_campaign_creation() {
		check_ajax_referer( 'houzez_ads_dashboard_nonce', 'nonce' );

		if ( ! houzez_ads_user_can_create_campaigns() ) {
			wp_send_json_error( array( 'message' => __( 'Permission denied.', 'houzez-ads-extension' ) ) );
		}

		// Simple placeholder for campaign creation
		wp_send_json_success( array( 'message' => 'Campaign creation placeholder' ) );
	}

	/**
	 * Handle campaign deletion AJAX.
	 */
	public function handle_campaign_deletion() {
		check_ajax_referer( 'houzez_ads_dashboard_nonce', 'nonce' );

		if ( ! houzez_ads_user_can_create_campaigns() ) {
			wp_send_json_error( array( 'message' => __( 'Permission denied.', 'houzez-ads-extension' ) ) );
		}

		$campaign_id = absint( $_POST['campaign_id'] ?? 0 );
		
		if ( ! $campaign_id ) {
			wp_send_json_error( array( 'message' => __( 'Invalid campaign ID.', 'houzez-ads-extension' ) ) );
		}

		// Check if user owns the campaign
		$campaign_post = get_post( $campaign_id );
		if ( ! $campaign_post || $campaign_post->post_author != get_current_user_id() ) {
			wp_send_json_error( array( 'message' => __( 'You can only delete your own campaigns.', 'houzez-ads-extension' ) ) );
		}

		// Delete campaign
		$result = wp_delete_post( $campaign_id, true );

		if ( ! $result ) {
			wp_send_json_error( array( 'message' => __( 'Failed to delete campaign.', 'houzez-ads-extension' ) ) );
		}

		wp_send_json_success( array( 'message' => __( 'Campaign deleted successfully!', 'houzez-ads-extension' ) ) );
	}

	/**
	 * Add campaign stats to dashboard overview.
	 */
	public function add_dashboard_stats() {
		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return;
		}

		$user_id = get_current_user_id();
		$user_credits = houzez_ads_get_user_credits( $user_id );
		$campaigns = get_posts( array(
			'post_type' => 'banner_campaign',
			'author' => $user_id,
			'posts_per_page' => -1,
			'post_status' => array( 'publish', 'draft', 'pending' )
		) );

		$active_campaigns = 0;
		$total_impressions = 0;
		$total_clicks = 0;

		foreach ( $campaigns as $campaign_post ) {
			$campaign = new Houzez_Banner_Campaign( $campaign_post );
			if ( $campaign->is_active() ) {
				$active_campaigns++;
				$analytics = $campaign->get_analytics();
				$total_impressions += $analytics['impressions'];
				$total_clicks += $analytics['clicks'];
			}
		}

		?>
		<div class="dashboard-stats-widget">
			<h4><?php _e( 'Ad Campaign Stats', 'houzez-ads-extension' ); ?></h4>
			<div class="row">
				<div class="col-md-2">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo number_format( $user_credits ); ?></span>
						<span class="stat-label"><?php _e( 'Available Credits', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
				<div class="col-md-2">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo count( $campaigns ); ?></span>
						<span class="stat-label"><?php _e( 'Total Campaigns', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
				<div class="col-md-2">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo $active_campaigns; ?></span>
						<span class="stat-label"><?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
				<div class="col-md-3">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo number_format( $total_impressions ); ?></span>
						<span class="stat-label"><?php _e( 'Total Views', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
				<div class="col-md-3">
					<div class="dashboard-stat-item">
						<span class="stat-number"><?php echo number_format( $total_clicks ); ?></span>
						<span class="stat-label"><?php _e( 'Total Clicks', 'houzez-ads-extension' ); ?></span>
					</div>
				</div>
			</div>
		</div>
		<?php
	}
}
