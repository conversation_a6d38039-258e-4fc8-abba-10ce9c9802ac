<?php
/**
 * Template Name: Ads Dashboard
 * 
 * Main dashboard page template for Houzez Ads Extension
 * Replicates the exact structure of Houzez user_dashboard.php
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Check if user is logged in
if ( ! is_user_logged_in() ) {
    wp_redirect( wp_login_url( get_permalink() ) );
    exit;
}

// Check user permissions
if ( ! houzez_ads_user_can_create_campaigns() ) {
    wp_redirect( home_url() );
    exit;
}

global $houzez_local;

// Get current user data
$userID = get_current_user_id();
$user_credits = houzez_ads_get_user_credits( $userID );
$campaign_counts = houzez_ads_get_user_campaign_counts( $userID );

// Determine which section to show
$ads_page = isset( $_GET['ads_page'] ) ? sanitize_text_field( $_GET['ads_page'] ) : 'overview';

// Handle credit check for campaign creation
if ( $ads_page === 'create_campaign' && $user_credits <= 0 ) {
    wp_redirect( add_query_arg( array( 'ads_page' => 'credits', 'notice' => 'insufficient_credits' ), get_permalink() ) );
    exit;
}

get_header('dashboard');

// Load the dashboard sidebar (same as other Houzez dashboard pages)
get_template_part('template-parts/dashboard/sidebar');
?>

<!-- Debug: Ads Dashboard Template Loaded -->
<!-- Ensure proper layout with inline styles as fallback -->
<style>
/* Force proper dashboard layout */
.dashboard-right {
    margin-left: 240px !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 100vh;
}

@media screen and (max-width: 1199px) {
    .dashboard-right {
        margin-left: 0 !important;
    }
}

.sidebar-collapsed .dashboard-right {
    margin-left: 0 !important;
}

/* Ensure sidebar is properly positioned */
.dashboard-sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
    width: 240px;
    z-index: 1000;
}

/* Debug styling */
.dashboard-right {
    border: 2px solid red !important;
    background: rgba(255, 0, 0, 0.1) !important;
}
</style>

<div class="dashboard-right">
    <!-- Dashboard Topbar -->
    <?php get_template_part('template-parts/dashboard/topbar'); ?>

    <div class="dashboard-content">
        <?php
        // Load appropriate ads dashboard section
        switch ( $ads_page ) {
            case 'overview':
            case 'dashboard':
                houzez_ads_get_template_part( 'template-parts/ads-dashboard/overview' );
                break;

            case 'campaigns':
                houzez_ads_get_template_part( 'template-parts/ads-dashboard/campaigns/main' );
                break;

            case 'create_campaign':
                houzez_ads_get_template_part( 'template-parts/ads-dashboard/campaigns/create' );
                break;

            case 'insights':
                houzez_ads_get_template_part( 'template-parts/ads-dashboard/insights/main' );
                break;

            case 'credits':
                houzez_ads_get_template_part( 'template-parts/ads-dashboard/credits/main' );
                break;

            default:
                houzez_ads_get_template_part( 'template-parts/ads-dashboard/overview' );
                break;
        }
        ?>
    </div>
</div>

<?php get_footer('dashboard'); ?>

<script>
jQuery(document).ready(function($) {
    // Initialize ads dashboard functionality
    if (typeof houzezAdsDashboard !== 'undefined') {
        houzezAdsDashboard.init();
    }
    
    // Handle credit check notifications
    <?php if ( isset( $_GET['notice'] ) && $_GET['notice'] === 'insufficient_credits' ) : ?>
        $('.dashboard-content-inner').prepend(
            '<div class="alert alert-warning alert-dismissible fade show" role="alert">' +
                '<strong><?php _e( 'Insufficient Credits', 'houzez-ads-extension' ); ?></strong> ' +
                '<?php _e( 'You need to purchase credits before creating a campaign.', 'houzez-ads-extension' ); ?>' +
                '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
            '</div>'
        );
    <?php endif; ?>
});
</script>
