/**
 * <PERSON><PERSON><PERSON> Ads Dashboard Styles
 * Replicates and extends <PERSON><PERSON><PERSON> dashboard styling for ad campaign management
 */

/* Ensure proper layout inheritance from <PERSON><PERSON><PERSON> dashboard */
body.houzez-dashboard-body {
    background-color: #f3f4f6;
}

/* Dashboard Layout - ensure compatibility with Ho<PERSON>z structure */
.houzez-ads-dashboard {
    font-family: "Plus Jakarta Sans", serif;
    background-color: #f3f4f6;
}

/* Ensure dashboard-right maintains proper layout */
.dashboard-right {
    margin-left: 240px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsive behavior to match <PERSON><PERSON><PERSON> */
@media screen and (max-width: 1199px) {
    .dashboard-right {
        margin-left: 0;
    }
}

.dashboard-content-block-wrap {
    margin-bottom: 2rem;
}

.dashboard-content-block {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.dashboard-content-block-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e9ecef;
}

/* Navigation Tabs */
.dashboard-tabs-wrap {
    padding: 0;
}

.dashboard-tabs-wrap .nav-tabs {
    border-bottom: 1px solid #e9ecef;
    margin: 0;
    padding: 0 2rem;
}

.dashboard-tabs-wrap .nav-link {
    color: #6c757d;
    border: none;
    border-bottom: 3px solid transparent;
    padding: 1rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.dashboard-tabs-wrap .nav-link:hover {
    color: #007bff;
    border-bottom-color: #007bff;
    background: none;
}

.dashboard-tabs-wrap .nav-link.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: none;
}

.dashboard-tabs-wrap .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Campaign Table Enhancements */
.houzez-data-content .table {
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.houzez-data-content .table thead th {
    background: var(--houzez-primary, #3498db);
    color: #fff;
    font-weight: 600;
    border: none;
    padding: 15px 12px;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.houzez-data-content .table tbody td {
    padding: 15px 12px;
    vertical-align: middle;
    border-bottom: 1px solid var(--houzez-border-light, #f1f3f4);
}

.houzez-data-content .table tbody tr:hover {
    background: var(--houzez-bg-light, #f8f9fa);
}

/* Campaign Status Badges */
.badge {
    font-size: 11px;
    font-weight: 600;
    padding: 6px 12px;
    border-radius: 20px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bg-success {
    background-color: var(--houzez-success, #28a745) !important;
}

.bg-warning {
    background-color: var(--houzez-warning, #ffc107) !important;
    color: #212529 !important;
}

.bg-danger {
    background-color: var(--houzez-danger, #dc3545) !important;
}

.bg-secondary {
    background-color: var(--houzez-secondary, #6c757d) !important;
}

.bg-dark {
    background-color: var(--houzez-dark, #343a40) !important;
}

/* Filter Controls */
.houzez-table-filters {
    background: #fff;
    padding: 20px;
    border-radius: 8px 8px 0 0;
    border-bottom: 1px solid var(--houzez-border-color, #e9ecef);
}

.dashboard-search-filter {
    position: relative;
}

.dashboard-search-filter span {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--houzez-text-muted, #6c757d);
    z-index: 2;
}

.dashboard-search-filter input {
    padding-left: 40px;
}

/* Action Buttons */
.dropdown-toggle {
    border: 1px solid var(--houzez-border-color, #e9ecef);
    background: #fff;
    color: var(--houzez-text-color, #333);
    transition: all 0.3s ease;
}

.dropdown-toggle:hover {
    background: var(--houzez-primary, #3498db);
    color: #fff;
    border-color: var(--houzez-primary, #3498db);
}

.dropdown-menu {
    border: 1px solid var(--houzez-border-color, #e9ecef);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border-radius: 6px;
}

.dropdown-item {
    padding: 10px 16px;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background: var(--houzez-primary-light, #e8f4fd);
    color: var(--houzez-primary, #3498db);
}

.dropdown-item.text-danger:hover {
    background: var(--houzez-danger-light, #f8d7da);
    color: var(--houzez-danger, #dc3545);
}

/* Empty State */
.text-center.py-5 {
    padding: 60px 20px !important;
}

.text-center.py-5 i {
    margin-bottom: 20px;
    opacity: 0.3;
}

/* Form Enhancements */
.form-step {
    margin-bottom: 30px;
}

.form-group.mb-3 {
    margin-bottom: 25px !important;
}

.form-label {
    font-weight: 600;
    color: var(--houzez-text-color, #333);
    margin-bottom: 8px;
}

.required-field {
    color: var(--houzez-danger, #dc3545);
    font-weight: 700;
}

.form-control:focus {
    border-color: var(--houzez-primary, #3498db);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.selectpicker {
    border: 1px solid var(--houzez-border-color, #ced4da) !important;
}

/* Alert Enhancements */
.alert {
    border-radius: 6px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-info {
    background: var(--houzez-info-bg, #d1ecf1);
    color: var(--houzez-info-text, #0c5460);
}

.alert-warning {
    background: var(--houzez-warning-bg, #fff3cd);
    color: var(--houzez-warning-text, #856404);
}

.alert-danger {
    background: var(--houzez-danger-bg, #f8d7da);
    color: var(--houzez-danger-text, #721c24);
}

/* Button Enhancements */
.btn-primary {
    background: var(--houzez-primary, #3498db);
    border-color: var(--houzez-primary, #3498db);
    font-weight: 600;
    padding: 10px 20px;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--houzez-primary-dark, #2980b9);
    border-color: var(--houzez-primary-dark, #2980b9);
    transform: translateY(-1px);
}

.btn-primary-outlined {
    background: transparent;
    color: var(--houzez-primary, #3498db);
    border: 2px solid var(--houzez-primary, #3498db);
}

.btn-primary-outlined:hover {
    background: var(--houzez-primary, #3498db);
    color: #fff;
}

/* Loading States */
.loader-ripple {
    display: inline-block;
    position: relative;
    width: 20px;
    height: 20px;
    margin-right: 8px;
}

.loader-ripple div {
    position: absolute;
    border: 2px solid #fff;
    opacity: 1;
    border-radius: 50%;
    animation: loader-ripple 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
}

.loader-ripple div:nth-child(2) {
    animation-delay: -0.5s;
}

@keyframes loader-ripple {
    0% {
        top: 8px;
        left: 8px;
        width: 0;
        height: 0;
        opacity: 1;
    }
    100% {
        top: 0px;
        left: 0px;
        width: 16px;
        height: 16px;
        opacity: 0;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-stat-item {
        margin-bottom: 10px;
    }
    
    .stat-number {
        font-size: 24px;
    }
    
    .houzez-table-filters .row > div {
        margin-bottom: 15px;
    }
    
    .table-responsive {
        font-size: 14px;
    }
    
    .dropdown-menu {
        position: static !important;
        transform: none !important;
        width: 100%;
        margin-top: 5px;
    }
}

/* Additional Dashboard Enhancements */
.campaign-analytics {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.analytics-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
}

.analytics-number {
    font-weight: 600;
    color: #2c3e50;
}

.analytics-label {
    color: #6c757d;
}

.credits-spent {
    font-weight: 600;
    color: #007bff;
    font-size: 1rem;
}

/* Control Checkboxes */
.control {
    position: relative;
    display: inline-block;
    margin: 0;
    cursor: pointer;
}

.control input {
    position: absolute;
    opacity: 0;
    z-index: -1;
}

.control__indicator {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    background: #fff;
    border: 2px solid #ddd;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.control input:checked ~ .control__indicator {
    background: #007bff;
    border-color: #007bff;
}

.control input:checked ~ .control__indicator:after {
    content: '';
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Bulk Actions */
.bulk-actions-bar {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    margin-bottom: 1rem;
    padding: 1rem;
}

.bulk-actions-bar .selected-count {
    font-weight: 600;
    color: #1976d2;
}

/* Dashboard Labels */
.dashboard-label {
    position: absolute;
    top: 4px;
    right: 4px;
    font-size: 0.6rem;
    padding: 2px 6px;
    border-radius: 3px;
    color: white;
    font-weight: 600;
    z-index: 2;
    line-height: 1;
}

.dashboard-label-featured {
    top: 4px;
    left: 4px;
    right: auto;
    background: #ffc107;
    padding: 2px 4px;
}

.dashboard-label-small {
    font-size: 0.5rem;
    padding: 1px 4px;
}

/* Image Placeholders */
.no-image-placeholder {
    border: 1px dashed #dee2e6;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Action Button Enhancements */
.action-btn {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.5rem;
    color: #6c757d;
    text-decoration: none;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
}

/* Stats Box Enhancements */
.stats-box {
    text-align: center;
    transition: transform 0.2s ease;
}

.stats-box:hover {
    transform: translateY(-2px);
}

.stats-box h2,
.stats-box h3 {
    margin: 0;
    font-weight: 700;
}

.stats-box .small {
    font-size: 0.875rem;
    opacity: 0.8;
}

/* Block Wrap */
.block-wrap {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.block-wrap .card-body {
    padding: 1.5rem;
}

.block-wrap .card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}
