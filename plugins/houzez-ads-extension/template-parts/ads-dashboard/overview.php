<?php
/**
 * Ads Dashboard Overview Template
 * Replicates the main dashboard overview similar to Houzez property dashboard
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$userID = get_current_user_id();
$user_credits = houzez_ads_get_user_credits( $userID );
$campaign_counts = houzez_ads_get_user_campaign_counts( $userID );
$recent_analytics = houzez_ads_get_user_analytics( $userID, date( 'Y-m-d', strtotime( '-30 days' ) ), date( 'Y-m-d' ) );

// Get recent campaigns
$recent_campaigns = get_posts( array(
    'post_type' => 'banner_campaign',
    'author' => $userID,
    'posts_per_page' => 5,
    'post_status' => array( 'publish', 'draft', 'pending' ),
    'orderby' => 'date',
    'order' => 'DESC'
) );

$dashboard_add_campaign = add_query_arg( 'ads_page', 'create_campaign', get_permalink() );
$dashboard_campaigns = add_query_arg( 'ads_page', 'campaigns', get_permalink() );
$dashboard_insights = add_query_arg( 'ads_page', 'insights', get_permalink() );
$dashboard_credits = add_query_arg( 'ads_page', 'credits', get_permalink() );
?>

<div class="heading d-flex align-items-center justify-content-between">
    <div class="heading-text">
        <h2><?php _e( 'Ad Campaign Dashboard', 'houzez-ads-extension' ); ?></h2>
        <p><?php _e( 'Manage your advertising campaigns and track performance', 'houzez-ads-extension' ); ?></p>
    </div>
</div>

<!-- Quick Stats Overview -->
<div class="property-stats">
    <div class="row">
        <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="stats-box">
                <div class="media">
                    <p><strong><?php _e( 'Total Campaigns', 'houzez-ads-extension' ); ?></strong></p>
                    <div class="icon-box">
                        <i class="houzez-icon icon-megaphone"></i>
                    </div>
                </div>
                <h3><?php echo number_format( $campaign_counts['total'] ); ?></h3>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="stats-box">
                <div class="media">
                    <p><strong><?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?></strong></p>
                    <div class="icon-box">
                        <i class="houzez-icon icon-check-circle-1"></i>
                    </div>
                </div>
                <h3><?php echo number_format( $campaign_counts['approved'] ); ?></h3>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="stats-box">
                <div class="media">
                    <p><strong><?php _e( 'Total Views', 'houzez-ads-extension' ); ?></strong></p>
                    <div class="icon-box">
                        <i class="houzez-icon icon-eye"></i>
                    </div>
                </div>
                <h3><?php echo number_format( $recent_analytics['total_impressions'] ); ?></h3>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="stats-box">
                <div class="media">
                    <p><strong><?php _e( 'Available Credits', 'houzez-ads-extension' ); ?></strong></p>
                    <div class="icon-box">
                        <i class="houzez-icon icon-accounting-document"></i>
                    </div>
                </div>
                <h3><?php echo number_format( $user_credits ); ?></h3>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="houzez-data-content">
    <div class="heading d-flex align-items-center justify-content-between">
        <div class="heading-text">
            <h2><?php _e( 'Quick Actions', 'houzez-ads-extension' ); ?></h2>
        </div>
    </div>

    <div class="row" style="padding: 24px;">
        <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="stats-box text-center" style="border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <a href="<?php echo esc_url( $dashboard_add_campaign ); ?>" style="text-decoration: none; color: inherit;">
                    <div class="icon-box" style="margin-bottom: 15px;">
                        <i class="houzez-icon icon-add-circle" style="font-size: 2rem; color: #007bff;"></i>
                    </div>
                    <h4 style="margin-bottom: 10px; font-size: 1.1rem;"><?php _e( 'Create Campaign', 'houzez-ads-extension' ); ?></h4>
                    <p style="margin: 0; color: #6c757d; font-size: 0.9rem;"><?php _e( 'Launch a new ad campaign', 'houzez-ads-extension' ); ?></p>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="stats-box text-center" style="border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <a href="<?php echo esc_url( $dashboard_campaigns ); ?>" style="text-decoration: none; color: inherit;">
                    <div class="icon-box" style="margin-bottom: 15px;">
                        <i class="houzez-icon icon-megaphone" style="font-size: 2rem; color: #28a745;"></i>
                    </div>
                    <h4 style="margin-bottom: 10px; font-size: 1.1rem;"><?php _e( 'Manage Campaigns', 'houzez-ads-extension' ); ?></h4>
                    <p style="margin: 0; color: #6c757d; font-size: 0.9rem;"><?php _e( 'View and edit your campaigns', 'houzez-ads-extension' ); ?></p>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="stats-box text-center" style="border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <a href="<?php echo esc_url( $dashboard_insights ); ?>" style="text-decoration: none; color: inherit;">
                    <div class="icon-box" style="margin-bottom: 15px;">
                        <i class="houzez-icon icon-analytics-pie-1" style="font-size: 2rem; color: #ffc107;"></i>
                    </div>
                    <h4 style="margin-bottom: 10px; font-size: 1.1rem;"><?php _e( 'View Insights', 'houzez-ads-extension' ); ?></h4>
                    <p style="margin: 0; color: #6c757d; font-size: 0.9rem;"><?php _e( 'Check campaign performance', 'houzez-ads-extension' ); ?></p>
                </a>
            </div>
        </div>

        <div class="col-lg-3 col-md-6 col-sm-12">
            <div class="stats-box text-center" style="border: 1px solid #e9ecef; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <a href="<?php echo esc_url( $dashboard_credits ); ?>" style="text-decoration: none; color: inherit;">
                    <div class="icon-box" style="margin-bottom: 15px;">
                        <i class="houzez-icon icon-accounting-document" style="font-size: 2rem; color: #dc3545;"></i>
                    </div>
                    <h4 style="margin-bottom: 10px; font-size: 1.1rem;"><?php _e( 'Buy Credits', 'houzez-ads-extension' ); ?></h4>
                    <p style="margin: 0; color: #6c757d; font-size: 0.9rem;"><?php _e( 'Purchase campaign credits', 'houzez-ads-extension' ); ?></p>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Campaigns -->
<?php if ( ! empty( $recent_campaigns ) ) : ?>
<div class="houzez-data-content">
    <div class="heading d-flex align-items-center justify-content-between">
        <div class="heading-text">
            <h2><?php _e( 'Recent Campaigns', 'houzez-ads-extension' ); ?></h2>
        </div>
        <div class="add-export-btn">
            <a href="<?php echo esc_url( $dashboard_campaigns ); ?>" class="btn btn-primary btn-sm">
                <?php _e( 'View All', 'houzez-ads-extension' ); ?>
            </a>
        </div>
    </div>

    <div class="houzez-data-table">
        <table class="table">
            <thead>
                <tr>
                    <th><?php _e( 'Campaign', 'houzez-ads-extension' ); ?></th>
                    <th><?php _e( 'Status', 'houzez-ads-extension' ); ?></th>
                    <th><?php _e( 'Zone', 'houzez-ads-extension' ); ?></th>
                    <th><?php _e( 'Performance', 'houzez-ads-extension' ); ?></th>
                    <th><?php _e( 'Actions', 'houzez-ads-extension' ); ?></th>
                </tr>
            </thead>
            <tbody>
                    <?php foreach ( $recent_campaigns as $campaign_post ) : 
                        $campaign = new Houzez_Banner_Campaign( $campaign_post );
                        $analytics = $campaign->get_analytics();
                        $ad_zones = houzez_ads_get_available_zones();
                        $statuses = houzez_ads_get_campaign_statuses();
                        
                        $status_class = 'bg-secondary';
                        switch ( $campaign->campaign_status ) {
                            case 'approved':
                                $status_class = 'bg-success';
                                break;
                            case 'pending':
                                $status_class = 'bg-warning';
                                break;
                            case 'rejected':
                                $status_class = 'bg-danger';
                                break;
                        }
                    ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if ( $campaign->banner_image ) : ?>
                                        <img src="<?php echo esc_url( $campaign->banner_image ); ?>" 
                                             alt="<?php echo esc_attr( $campaign->banner_alt ); ?>"
                                             class="me-3" 
                                             style="width: 50px; height: 35px; object-fit: cover; border-radius: 4px;" />
                                    <?php endif; ?>
                                    <div>
                                        <strong><?php echo esc_html( $campaign->title ); ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?php printf( __( 'Created: %s', 'houzez-ads-extension' ), date_i18n( get_option( 'date_format' ), strtotime( $campaign_post->post_date ) ) ); ?>
                                        </small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge <?php echo esc_attr( $status_class ); ?>">
                                    <?php echo esc_html( $statuses[ $campaign->campaign_status ] ?? $campaign->campaign_status ); ?>
                                </span>
                            </td>
                            <td>
                                <?php echo esc_html( $ad_zones[ $campaign->ad_zone ] ?? $campaign->ad_zone ); ?>
                            </td>
                            <td>
                                <?php if ( $campaign->campaign_status === 'approved' ) : ?>
                                    <div class="small">
                                        <div><?php printf( __( 'Views: %s', 'houzez-ads-extension' ), number_format( $analytics['impressions'] ) ); ?></div>
                                        <div><?php printf( __( 'Clicks: %s', 'houzez-ads-extension' ), number_format( $analytics['clicks'] ) ); ?></div>
                                    </div>
                                <?php else : ?>
                                    <span class="text-muted"><?php _e( 'N/A', 'houzez-ads-extension' ); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?php echo esc_url( add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_id' => $campaign->id ), get_permalink() ) ); ?>" 
                                   class="btn btn-sm btn-outline-primary">
                                    <?php _e( 'View', 'houzez-ads-extension' ); ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
</div>
<?php endif; ?>

<!-- Performance Chart -->
<div class="houzez-data-content">
    <div class="heading d-flex align-items-center justify-content-between">
        <div class="heading-text">
            <h2><?php _e( 'Campaign Performance (Last 30 Days)', 'houzez-ads-extension' ); ?></h2>
        </div>
    </div>

    <div style="padding: 24px;">
        <canvas id="campaignOverviewChart" height="300"></canvas>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Initialize overview chart
    if (typeof Chart !== 'undefined') {
        var ctx = document.getElementById('campaignOverviewChart');
        if (ctx) {
            var chartData = <?php echo json_encode( $recent_analytics['chart_data'] ); ?>;
            
            new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });
        }
    }
});
</script>
