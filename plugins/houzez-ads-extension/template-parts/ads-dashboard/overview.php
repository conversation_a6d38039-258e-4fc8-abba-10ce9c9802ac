<?php
/**
 * Ads Dashboard Overview Template
 * Replicates the main dashboard overview similar to Houzez property dashboard
 *
 * @package HouzezAdsExtension
 */

// Security check
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

$userID = get_current_user_id();
$user_credits = houzez_ads_get_user_credits( $userID );
$campaign_counts = houzez_ads_get_user_campaign_counts( $userID );
$recent_analytics = houzez_ads_get_user_analytics( $userID, date( 'Y-m-d', strtotime( '-30 days' ) ), date( 'Y-m-d' ) );

// Get recent campaigns
$recent_campaigns = get_posts( array(
    'post_type' => 'banner_campaign',
    'author' => $userID,
    'posts_per_page' => 5,
    'post_status' => array( 'publish', 'draft', 'pending' ),
    'orderby' => 'date',
    'order' => 'DESC'
) );

$dashboard_add_campaign = add_query_arg( 'ads_page', 'create_campaign', get_permalink() );
$dashboard_campaigns = add_query_arg( 'ads_page', 'campaigns', get_permalink() );
$dashboard_insights = add_query_arg( 'ads_page', 'insights', get_permalink() );
$dashboard_credits = add_query_arg( 'ads_page', 'credits', get_permalink() );
?>

<div class="heading d-flex align-items-center justify-content-between">
    <div class="heading-text">
        <h2><?php _e( 'Ad Campaign Dashboard', 'houzez-ads-extension' ); ?></h2>
        <p><?php _e( 'Manage your advertising campaigns and track performance', 'houzez-ads-extension' ); ?></p>
    </div>
</div>

<!-- Campaign Statistics - Using exact Houzez structure -->
<div class="property-stats mt-3">
    <div class="row">
        <div class="col-lg-3 col-md-4 col-sm-6 col-12">
            <div class="stats-box">
                <div class="media">
                    <p><strong><?php _e( 'Total Campaigns', 'houzez-ads-extension' ); ?></strong></p>
                    <div class="icon-box">
                        <i class="houzez-icon icon-megaphone"></i>
                    </div>
                </div>
                <h3><?php echo number_format_i18n( $campaign_counts['total'] ); ?></h3>
            </div>
        </div>

        <div class="col-lg-3 col-md-4 col-sm-6 col-12">
            <div class="stats-box">
                <div class="media">
                    <p><strong><?php _e( 'Active Campaigns', 'houzez-ads-extension' ); ?></strong></p>
                    <div class="icon-box">
                        <i class="houzez-icon icon-check-circle-1"></i>
                    </div>
                </div>
                <h3><?php echo number_format_i18n( $campaign_counts['approved'] ); ?></h3>
            </div>
        </div>

        <div class="col-lg-3 col-md-4 col-sm-6 col-12">
            <div class="stats-box">
                <div class="media">
                    <p><strong><?php _e( 'Total Views', 'houzez-ads-extension' ); ?></strong></p>
                    <div class="icon-box">
                        <i class="houzez-icon icon-eye"></i>
                    </div>
                </div>
                <h3><?php echo number_format_i18n( $recent_analytics['total_impressions'] ); ?></h3>
            </div>
        </div>

        <div class="col-lg-3 col-md-4 col-sm-6 col-12">
            <div class="stats-box">
                <div class="media">
                    <p><strong><?php _e( 'Available Credits', 'houzez-ads-extension' ); ?></strong></p>
                    <div class="icon-box">
                        <i class="houzez-icon icon-accounting-document"></i>
                    </div>
                </div>
                <h3><?php echo number_format_i18n( $user_credits ); ?></h3>
            </div>
        </div>
    </div>
</div>



<!-- Recent Campaigns -->
<?php if ( ! empty( $recent_campaigns ) ) : ?>
<div class="houzez-data-content">
    <div class="heading d-flex align-items-center justify-content-between">
        <div class="heading-text">
            <h2><?php _e( 'Recent Campaigns', 'houzez-ads-extension' ); ?></h2>
        </div>
        <div class="add-export-btn">
            <a href="<?php echo esc_url( $dashboard_campaigns ); ?>" class="btn btn-primary btn-sm">
                <?php _e( 'View All Campaigns', 'houzez-ads-extension' ); ?>
            </a>
        </div>
    </div>

    <div class="houzez-data-table table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th><?php _e( 'Campaign', 'houzez-ads-extension' ); ?></th>
                    <th><?php _e( 'Status', 'houzez-ads-extension' ); ?></th>
                    <th><?php _e( 'Performance', 'houzez-ads-extension' ); ?></th>
                    <th><?php _e( 'Actions', 'houzez-ads-extension' ); ?></th>
                </tr>
            </thead>
            <tbody>
                    <?php foreach ( $recent_campaigns as $campaign_post ) :
                        $campaign = new Houzez_Banner_Campaign( $campaign_post );
                        $analytics = $campaign->get_analytics();
                        $statuses = houzez_ads_get_campaign_statuses();

                        $status_class = 'status';
                        switch ( $campaign->campaign_status ) {
                            case 'approved':
                                $status_class .= ' status-active';
                                break;
                            case 'pending':
                                $status_class .= ' status-pending';
                                break;
                            case 'rejected':
                                $status_class .= ' status-expired';
                                break;
                        }
                    ?>
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php if ( $campaign->banner_image ) : ?>
                                        <div class="image-holder me-3">
                                            <img src="<?php echo esc_url( $campaign->banner_image ); ?>"
                                                 alt="<?php echo esc_attr( $campaign->banner_alt ); ?>"
                                                 style="width: 50px; height: 35px; object-fit: cover; border-radius: 4px;" />
                                        </div>
                                    <?php endif; ?>
                                    <div class="text-box">
                                        <h5><a href="<?php echo esc_url( add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_id' => $campaign->id ), get_permalink() ) ); ?>">
                                            <?php echo esc_html( $campaign->title ); ?>
                                        </a></h5>
                                        <address><?php printf( __( 'Created: %s', 'houzez-ads-extension' ), date_i18n( get_option( 'date_format' ), strtotime( $campaign_post->post_date ) ) ); ?></address>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="<?php echo esc_attr( $status_class ); ?>">
                                    <?php echo esc_html( $statuses[ $campaign->campaign_status ] ?? ucfirst( $campaign->campaign_status ) ); ?>
                                </span>
                            </td>
                            <td>
                                <?php if ( $campaign->campaign_status === 'approved' ) : ?>
                                    <div class="small">
                                        <div><?php printf( __( 'Views: %s', 'houzez-ads-extension' ), number_format_i18n( $analytics['impressions'] ) ); ?></div>
                                        <div><?php printf( __( 'Clicks: %s', 'houzez-ads-extension' ), number_format_i18n( $analytics['clicks'] ) ); ?></div>
                                    </div>
                                <?php else : ?>
                                    <span class="text-muted"><?php _e( 'N/A', 'houzez-ads-extension' ); ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?php echo esc_url( add_query_arg( array( 'ads_page' => 'campaigns', 'campaign_id' => $campaign->id ), get_permalink() ) ); ?>"
                                   class="btn btn-sm btn-outline-primary">
                                    <?php _e( 'View', 'houzez-ads-extension' ); ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
</div>
<?php endif; ?>

<!-- Performance Chart -->
<div class="houzez-data-content">
    <div class="heading d-flex align-items-center justify-content-between">
        <div class="heading-text">
            <h2><?php _e( 'Campaign Performance (Last 30 Days)', 'houzez-ads-extension' ); ?></h2>
        </div>
    </div>

    <div style="padding: 24px;">
        <canvas id="campaignOverviewChart" height="300"></canvas>
    </div>
</div>

<script>
jQuery(document).ready(function($) {
    // Initialize overview chart
    if (typeof Chart !== 'undefined') {
        var ctx = document.getElementById('campaignOverviewChart');
        if (ctx) {
            var chartData = <?php echo json_encode( $recent_analytics['chart_data'] ); ?>;
            
            new Chart(ctx, {
                type: 'line',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top',
                        }
                    }
                }
            });
        }
    }
});
</script>
